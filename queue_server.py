import asyncio
import json
import uuid
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from collections import deque

import openai
from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

# Configuration
OPENAI_API_KEY = "************************************************************************************************************************************"
openai_client = openai.AsyncOpenAI(api_key=OPENAI_API_KEY)

app = FastAPI()

# Serve static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@dataclass
class QueuedMessage:
    id: str
    username: str
    text: str
    timestamp: float
    status: str = "pending"  # pending, processing, completed

@dataclass
class StreamResponse:
    type: str  # "queue_update", "response_start", "response_chunk", "response_end"
    data: dict

class ChatQueue:
    def __init__(self):
        self.queue: deque = deque()
        self.processing = False
        self.current_message: Optional[QueuedMessage] = None
        self.websockets: List[WebSocket] = []
    
    async def add_message(self, username: str, text: str) -> str:
        message_id = str(uuid.uuid4())[:8]
        message = QueuedMessage(
            id=message_id,
            username=username,
            text=text,
            timestamp=asyncio.get_event_loop().time()
        )
        self.queue.append(message)
        
        # Notify all connected clients about queue update
        await self.broadcast_queue_update()
        
        # Start processing if not already processing
        if not self.processing:
            asyncio.create_task(self.process_queue())
        
        return message_id
    
    async def process_queue(self):
        if self.processing or len(self.queue) == 0:
            return
        
        self.processing = True
        
        while self.queue:
            message = self.queue.popleft()
            self.current_message = message
            message.status = "processing"
            
            await self.broadcast_queue_update()
            
            try:
                # Get GPT response
                response = await self.get_gpt_response(message.text)
                if response:
                    await self.stream_response(message, response)
                
                message.status = "completed"
                
            except Exception as e:
                print(f"Error processing message {message.id}: {e}")
                message.status = "error"
            
            await self.broadcast_queue_update()
            
            # Small delay between messages
            await asyncio.sleep(1)
        
        self.current_message = None
        self.processing = False
        await self.broadcast_queue_update()
    
    async def get_gpt_response(self, text: str) -> Optional[str]:
        try:
            response = await openai_client.chat.completions.create(
                model="gpt-4o-mini",
                max_tokens=150,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are Alice, a friendly AI avatar. Respond conversationally in 1-2 sentences. Be engaging and ask follow-up questions."
                    },
                    {"role": "user", "content": text}
                ],
                stream=True
            )
            
            full_response = ""
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    # Stream each chunk to frontend
                    await self.broadcast_response_chunk(content)
            
            return full_response
            
        except Exception as e:
            print(f"GPT error: {e}")
            return None
    
    async def stream_response(self, message: QueuedMessage, response: str):
        # Notify start of response
        await self.broadcast({
            "type": "response_start",
            "data": {
                "message_id": message.id,
                "username": message.username,
                "question": message.text
            }
        })
        
        # Response chunks are sent in get_gpt_response
        
        # Notify end of response
        await self.broadcast({
            "type": "response_end",
            "data": {
                "message_id": message.id,
                "full_response": response
            }
        })
    
    async def broadcast_response_chunk(self, chunk: str):
        await self.broadcast({
            "type": "response_chunk",
            "data": {"chunk": chunk}
        })
    
    async def broadcast_queue_update(self):
        queue_data = {
            "queue": [asdict(msg) for msg in self.queue],
            "current": asdict(self.current_message) if self.current_message else None,
            "processing": self.processing
        }
        
        await self.broadcast({
            "type": "queue_update",
            "data": queue_data
        })
    
    async def broadcast(self, message: dict):
        if not self.websockets:
            return
        
        disconnected = []
        for ws in self.websockets:
            try:
                await ws.send_text(json.dumps(message))
            except:
                disconnected.append(ws)
        
        # Remove disconnected websockets
        for ws in disconnected:
            self.websockets.remove(ws)
    
    def add_websocket(self, websocket: WebSocket):
        self.websockets.append(websocket)
    
    def remove_websocket(self, websocket: WebSocket):
        if websocket in self.websockets:
            self.websockets.remove(websocket)

# Global chat queue
chat_queue = ChatQueue()

# API Models
class ChatMessage(BaseModel):
    username: str
    text: str

class QueueStatus(BaseModel):
    queue_length: int
    processing: bool
    current_message: Optional[dict]

# API Endpoints
@app.post("/api/chat")
async def send_message(message: ChatMessage):
    if not message.text.strip():
        raise HTTPException(400, "Message cannot be empty")
    
    message_id = await chat_queue.add_message(message.username, message.text.strip())
    
    return {
        "status": "queued",
        "message_id": message_id,
        "queue_position": len(chat_queue.queue)
    }

@app.get("/api/queue/status")
async def get_queue_status():
    return QueueStatus(
        queue_length=len(chat_queue.queue),
        processing=chat_queue.processing,
        current_message=asdict(chat_queue.current_message) if chat_queue.current_message else None
    )

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    chat_queue.add_websocket(websocket)
    
    try:
        # Send initial queue status
        await chat_queue.broadcast_queue_update()
        
        # Keep connection alive
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        chat_queue.remove_websocket(websocket)

@app.get("/")
async def get_viewer():
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Chat Queue Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 800px; margin: 0 auto; }
            .chat-input { margin: 20px 0; }
            .chat-input input { padding: 10px; width: 300px; margin-right: 10px; }
            .chat-input button { padding: 10px 20px; }
            .queue-status { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .response-area { background: #e8f4fd; padding: 15px; margin: 20px 0; border-radius: 5px; min-height: 100px; }
            .tts-controls { margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Chat Queue Test</h1>
            
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type your message..." maxlength="200">
                <input type="text" id="usernameInput" placeholder="Username" value="TestUser" style="width: 100px;">
                <button onclick="sendMessage()">Send</button>
            </div>
            
            <div class="queue-status">
                <h3>Queue Status</h3>
                <div id="queueInfo">Connecting...</div>
            </div>
            
            <div class="response-area">
                <h3>Avatar Response</h3>
                <div id="responseText"></div>
                <div class="tts-controls">
                    <button onclick="stopTTS()">Stop TTS</button>
                    <span id="ttsStatus"></span>
                </div>
            </div>
            <button id="eee">Speak</button>
        </div>

        <script>
                 document.querySelector("button#eee")
    .addEventListener("click", () => {
        const utterance = new SpeechSynthesisUtterance("Hello, this is working!");
        window.speechSynthesis.speak(utterance);
    });

            let ws = null;
            let currentResponse = '';
            let speechBuffer = '';
            let speechSynthesis = window.speechSynthesis;
            let currentUtterance = null;

            function connectWebSocket() {
                ws = new WebSocket('ws://localhost:8000/ws');
                
                ws.onopen = function() {
                    console.log('WebSocket connected');
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                };
                
                ws.onclose = function() {
                    console.log('WebSocket disconnected');
                    setTimeout(connectWebSocket, 3000);
                };
            }

            function handleWebSocketMessage(message) {
                switch(message.type) {
                    case 'queue_update':
                        updateQueueStatus(message.data);
                        break;
                    case 'response_start':
                        startResponse(message.data);
                        break;
                    case 'response_chunk':
                        speakText(message.data.chunk.trim())
                        addResponseChunk(message.data.chunk);
                        break;
                    case 'response_end':
                        endResponse(message.full_response);
                        break;
                }
            }

            function updateQueueStatus(data) {
                const queueInfo = document.getElementById('queueInfo');
                let html = `Queue Length: ${data.queue.length}<br>`;
                html += `Processing: ${data.processing}<br>`;
                
                if (data.current) {
                    html += `Current: ${data.current.username} - "${data.current.text}"<br>`;
                }
                
                if (data.queue.length > 0) {
                    html += '<br>Pending:<br>';
                    data.queue.forEach((msg, i) => {
                        html += `${i+1}. ${msg.username}: "${msg.text}"<br>`;
                    });
                }
                
                queueInfo.innerHTML = html;
            }

            function startResponse(data) {
                currentResponse = '';
                speechBuffer = '';
                document.getElementById('responseText').innerHTML = `<strong>Responding to ${data.username}:</strong> "${data.question}"<br><br>`;
                document.getElementById('ttsStatus').textContent = 'Preparing to speak...';
                
                // Stop any current TTS
                stopTTS();
            }

            function addResponseChunk(chunk) {
                currentResponse += chunk;
                speechBuffer += chunk;
                document.getElementById('responseText').innerHTML += chunk;
                
                // Just accumulate text, don't speak yet
            }

            function endResponse(data) {
                // Now speak the complete response
                if (speechBuffer.trim()) {
                    document.getElementById('ttsStatus').textContent = 'Speaking...';
                    speakText(speechBuffer.trim());
                } else {
                    document.getElementById('ttsStatus').textContent = 'Response complete';
                }
                
                console.log('Full response:', data.full_response);
            }

            function speakText(text) {
    if (!text.trim()) return;
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1.1;
    utterance.volume = 0.8;

    let voices = synth.getVoices();
    if (voices.length === 0) {
        synth.onvoiceschanged = () => {
            voices = synth.getVoices();
            pickVoiceAndSpeak(utterance, voices);
        };
    } else {
        pickVoiceAndSpeak(utterance, voices);
    }
}

function pickVoiceAndSpeak(utterance, voices) {
    const femaleVoice = voices.find(v => 
        v.name.includes('Samantha') || 
        v.name.includes('Karen') || 
        v.name.includes('Susan') || 
        v.name.includes('Zira')
    );
    if (femaleVoice) utterance.voice = femaleVoice;
    synth.speak(utterance);
}


            function stopTTS() {
                speechSynthesis.cancel();
                speechBuffer = '';
                document.getElementById('ttsStatus').textContent = '';
            }

            async function sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const usernameInput = document.getElementById('usernameInput');
                const text = messageInput.value.trim();
                const username = usernameInput.value.trim() || 'Anonymous';
                
                if (!text) return;
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username, text })
                    });
                    
                    const result = await response.json();
                    console.log('Message sent:', result);
                    messageInput.value = '';
                } catch (error) {
                    console.error('Error sending message:', error);
                }
            }

            // Enter key to send
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Initialize
            connectWebSocket();
            
            // Load voices when available
            speechSynthesis.onvoiceschanged = function() {
                console.log('Available voices:', speechSynthesis.getVoices().map(v => v.name));
            };
        </script>
    </body>
    </html>
    """)

if __name__ == "__main__":
    print("Starting Chat Queue Server...")
    print("Make sure to set your OPENAI_API_KEY in the code!")
    print("Visit http://localhost:8000 to test the queue system")
    uvicorn.run(app, host="0.0.0.0", port=8000)
