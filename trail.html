<!DOCTYPE html>
    <html>
    <head>
        <title>Chat Queue Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 800px; margin: 0 auto; }
            .chat-input { margin: 20px 0; }
            .chat-input input { padding: 10px; width: 300px; margin-right: 10px; }
            .chat-input button { padding: 10px 20px; }
            .queue-status { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .response-area { background: #e8f4fd; padding: 15px; margin: 20px 0; border-radius: 5px; min-height: 100px; }
            .tts-controls { margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Chat Queue Test</h1>
            
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type your message..." maxlength="200">
                <input type="text" id="usernameInput" placeholder="Username" value="TestUser" style="width: 100px;">
                <button onclick="sendMessage()">Send</button>
            </div>
            
            <div class="queue-status">
                <h3>Queue Status</h3>
                <div id="queueInfo">Connecting...</div>
            </div>
            
            <div class="response-area">
                <h3>Avatar Response</h3>
                <div id="responseText"></div>
                <div class="tts-controls">
                    <button onclick="stopTTS()">Stop TTS</button>
                    <span id="ttsStatus"></span>
                </div>
            </div>
            <button onclick="speakTextResponse()">Speak</button>
        </div>

        <script>
                 document.querySelector("button[onclick='speakTextResponse()']")
    .addEventListener("click", () => {
        const utterance = new SpeechSynthesisUtterance("Hello, this is working!");
        window.speechSynthesis.speak(utterance);
    });

            let ws = null;
            let currentResponse = '';
            let speechBuffer = '';
            let speechSynthesis = window.speechSynthesis;
            let currentUtterance = null;

            function connectWebSocket() {
                ws = new WebSocket('ws://localhost:8000/ws');
                
                ws.onopen = function() {
                    console.log('WebSocket connected');
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                };
                
                ws.onclose = function() {
                    console.log('WebSocket disconnected');
                    setTimeout(connectWebSocket, 3000);
                };
            }

            function handleWebSocketMessage(message) {
                switch(message.type) {
                    case 'queue_update':
                        updateQueueStatus(message.data);
                        break;
                    case 'response_start':
                        startResponse(message.data);
                        break;
                    case 'response_chunk':
                        addResponseChunk(message.data.chunk);
                        break;
                    case 'response_end':
                        endResponse(message.data);
                        break;
                }
            }

            function updateQueueStatus(data) {
                const queueInfo = document.getElementById('queueInfo');
                let html = `Queue Length: ${data.queue.length}<br>`;
                html += `Processing: ${data.processing}<br>`;
                
                if (data.current) {
                    html += `Current: ${data.current.username} - "${data.current.text}"<br>`;
                }
                
                if (data.queue.length > 0) {
                    html += '<br>Pending:<br>';
                    data.queue.forEach((msg, i) => {
                        html += `${i+1}. ${msg.username}: "${msg.text}"<br>`;
                    });
                }
                
                queueInfo.innerHTML = html;
            }

            function startResponse(data) {
                currentResponse = '';
                speechBuffer = '';
                document.getElementById('responseText').innerHTML = `<strong>Responding to ${data.username}:</strong> "${data.question}"<br><br>`;
                document.getElementById('ttsStatus').textContent = 'Preparing to speak...';
                
                // Stop any current TTS
                stopTTS();
            }

            function addResponseChunk(chunk) {
                currentResponse += chunk;
                speechBuffer += chunk;
                document.getElementById('responseText').innerHTML += chunk;
                
                // Just accumulate text, don't speak yet
            }

            function endResponse(data) {
                // Now speak the complete response
                if (speechBuffer.trim()) {
                    document.getElementById('ttsStatus').textContent = 'Speaking...';
                    speakText(speechBuffer.trim());
                } else {
                    document.getElementById('ttsStatus').textContent = 'Response complete';
                }
                
                console.log('Full response:', data.full_response);
            }

            function speakText(text) {
    if (!text.trim()) return;
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1.1;
    utterance.volume = 0.8;

    let voices = synth.getVoices();
    if (voices.length === 0) {
        synth.onvoiceschanged = () => {
            voices = synth.getVoices();
            pickVoiceAndSpeak(utterance, voices);
        };
    } else {
        pickVoiceAndSpeak(utterance, voices);
    }
}

function pickVoiceAndSpeak(utterance, voices) {
    const femaleVoice = voices.find(v => 
        v.name.includes('Samantha') || 
        v.name.includes('Karen') || 
        v.name.includes('Susan') || 
        v.name.includes('Zira')
    );
    if (femaleVoice) utterance.voice = femaleVoice;
    synth.speak(utterance);
}


            function stopTTS() {
                speechSynthesis.cancel();
                speechBuffer = '';
                document.getElementById('ttsStatus').textContent = '';
            }

            async function sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const usernameInput = document.getElementById('usernameInput');
                const text = messageInput.value.trim();
                const username = usernameInput.value.trim() || 'Anonymous';
                
                if (!text) return;
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username, text })
                    });
                    
                    const result = await response.json();
                    console.log('Message sent:', result);
                    messageInput.value = '';
                } catch (error) {
                    console.error('Error sending message:', error);
                }
            }

            // Enter key to send
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Initialize
            connectWebSocket();
            
            // Load voices when available
            speechSynthesis.onvoiceschanged = function() {
                console.log('Available voices:', speechSynthesis.getVoices().map(v => v.name));
            };
        </script>
    </body>
    </html>