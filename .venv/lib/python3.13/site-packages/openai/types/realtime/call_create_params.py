# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing_extensions import Required, TypedDict

from .realtime_session_create_request_param import RealtimeSessionCreateRequestParam

__all__ = ["CallCreateParams"]


class CallCreateParams(TypedDict, total=False):
    sdp: Required[str]
    """WebRTC Session Description Protocol (SDP) offer generated by the caller."""

    session: RealtimeSessionCreateRequestParam
    """Realtime session object configuration."""
