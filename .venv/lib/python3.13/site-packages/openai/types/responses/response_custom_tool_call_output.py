# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import List, Union, Optional
from typing_extensions import Literal, Annotated, TypeAlias

from ..._utils import PropertyInfo
from ..._models import BaseModel
from .response_input_file import ResponseInputFile
from .response_input_text import ResponseInputText
from .response_input_image import ResponseInputImage

__all__ = ["ResponseCustomToolCallOutput", "OutputOutputContentList"]

OutputOutputContentList: TypeAlias = Annotated[
    Union[ResponseInputText, ResponseInputImage, ResponseInputFile], PropertyInfo(discriminator="type")
]


class ResponseCustomToolCallOutput(BaseModel):
    call_id: str
    """The call ID, used to map this custom tool call output to a custom tool call."""

    output: Union[str, List[OutputOutputContentList]]
    """
    The output from the custom tool call generated by your code. Can be a string or
    an list of output content.
    """

    type: Literal["custom_tool_call_output"]
    """The type of the custom tool call output. Always `custom_tool_call_output`."""

    id: Optional[str] = None
    """The unique ID of the custom tool call output in the OpenAI platform."""
