# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import List, Union, Optional
from typing_extensions import Literal, Annotated, TypeAlias

from ..._utils import PropertyInfo
from ..._models import BaseModel
from .response_input_file import ResponseInputFile
from .response_input_text import ResponseInputText
from .response_input_image import ResponseInputImage

__all__ = ["ResponseFunctionToolCallOutputItem", "OutputOutputContentList"]

OutputOutputContentList: TypeAlias = Annotated[
    Union[ResponseInputText, ResponseInputImage, ResponseInputFile], PropertyInfo(discriminator="type")
]


class ResponseFunctionToolCallOutputItem(BaseModel):
    id: str
    """The unique ID of the function call tool output."""

    call_id: str
    """The unique ID of the function tool call generated by the model."""

    output: Union[str, List[OutputOutputContentList]]
    """
    The output from the function call generated by your code. Can be a string or an
    list of output content.
    """

    type: Literal["function_call_output"]
    """The type of the function tool call output. Always `function_call_output`."""

    status: Optional[Literal["in_progress", "completed", "incomplete"]] = None
    """The status of the item.

    One of `in_progress`, `completed`, or `incomplete`. Populated when items are
    returned via API.
    """
